# register tasks
from bisheng.worker.test.test import *
from bisheng.worker.knowledge.file_worker import *
from bisheng.worker.workflow.tasks import *

# Export celery app - handle circular import gracefully
try:
    from bisheng.worker.main import bisheng_celery as celery_app
except ImportError:
    # During initialization, main might not be available yet
    celery_app = None

def __getattr__(name):
    if name == 'celery_app':
        if celery_app is None:
            # Try to import again if it wasn't available during module init
            try:
                from bisheng.worker.main import bisheng_celery
                globals()['celery_app'] = bisheng_celery
                return bisheng_celery
            except ImportError as e:
                raise ImportError(f"Cannot import celery_app: {e}")
        return celery_app
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
